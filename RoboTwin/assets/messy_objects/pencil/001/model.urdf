<?xml version='1.0' encoding='utf-8'?>
<robot name="pencil">
  <link name="baseLink">
    <contact>
      <friction_anchor />
      <lateral_friction value="0.3" />
      <rolling_friction value="0.0" />
      <contact_cfm value="0.0" />
      <contact_erp value="1.0" />
    </contact>
    <inertial>
       <origin rpy="0 0 0" xyz="0 0 0" />
       <mass value="0.01" />
       <inertia ixx="1e-06" ixy="0" ixz="0" iyy="1e-05" iyz="0" izz="1e-05" />
    </inertial>
    <visual>
      <geometry>
        <cylinder radius="0.003" length="0.15"/>
      </geometry>
      <material name="yellow">
        <color rgba="1 1 0 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.003" length="0.15"/>
      </geometry>
    </collision>
  </link>
</robot>
